
document.addEventListener('DOMContentLoaded', function() {

    class Instance {
        constructor(instance, versions) {
            this.instance = instance
            this.latestVersion = Instance.getLatestVersion(this.instance, versions)

            if (this.latestVersion) {
                let semverCurrent = new SemVer(this.instance.Version.Comparable)
                let semverLatest = new SemVer(this.latestVersion.Comparable)

                this.update = new Update(semverCurrent, semverLatest)
                this.updateType = this.update.getType()
            } else {
                this.updateType = ''
            }
        }

        createHtmlElement() {

            let qualifiers = ''
            for (const qKey in this.instance.Qualifiers) {
                if (qKey.startsWith("_")) {
                    continue
                }
                qualifiers += `<div class="break-all"><span class="font-bold">${qKey}</span>: ${this.instance.Qualifiers[qKey]}</div>`
            }

            let parameters = ''
            let parameterButons = ''
            for (const pKey in this.instance.Parameters) {
                if (pKey.startsWith("_")) {
                    // Skip internal parameters
                    continue
                } else if (this.instance.Parameters[pKey].startsWith('https://')) {
                    // Create button for URLs
                    parameterButons += `<a class="rounded-xl bg-vf-gray hover:bg-vf-dark-gray text-white px-4 m-2" href=${this.instance.Parameters[pKey]} target="_blank">${pKey}</a>`
                } else if (this.instance.Parameters[pKey].startsWith('badge:')) {
                    // Handle inline badge data format: badge:status:color
                    const badgeData = this.instance.Parameters[pKey].substring(6).split(':')
                    if (badgeData.length === 2) {
                        const [status, color] = badgeData
                        parameters += `<div class="py-1 flex justify-center">
                            ${this.createInlineBadge(status, color)}
                        </div>`
                    }
                } else {
                    // Display as regular text parameter
                    parameters += `<div class="text-sm italic break-all"><span class="font-bold">${pKey}</span>: ${this.instance.Parameters[pKey]}</div>`
                }
            }

            let latest = ''
            let updateColorClasses = ''
            if (this.latestVersion) {
                latest = `
                    <div class="w-full flex flex-row justify-center">
                        <p class="font-semi-bold">Latest: </p>
                        <p class="flex-shrink truncate text-ellipsis" style="direction: rtl">
                            ${this.latestVersion.FullVersion}
                        </p>
                    </div>`
                if (this.update) {
                    updateColorClasses = this.update.tailwindBorderClasses()
                }
            }

            return `
                <div class="flex flex-col justify-center border border-neutral-300 bg-white rounded-xl p-2 m-1 w-1/5 text-center break-words shadow ${updateColorClasses}">
                    ${qualifiers}
                    <hr />
                    ${parameters}
                    <hr />
                    ${parameterButons}
                    <hr />
                    <div class="group relative">
                        <p class="truncate text-ellipsis" style="direction: rtl">${this.instance.Version.FullVersion}</p>
                        <span class="pointer-events-none absolute -top-7 -translate-x-1/2 w-max opacity-0 transition-opacity group-hover:opacity-100 group-hover:bg-vf-aqua-blue text-white px-2 rounded-xl  shadow">
                        ${this.instance.Version.FullVersion}
                        </span>
                        ${latest}
                    </div>
                    <div class="flex-grow"></div>
                </div>
            `
        }

        getUpdateType() {
            return this.updateType
        }

        // createInlineBadge generates a CSS-based badge for better browser compatibility
        createInlineBadge(status, color) {
            return `<div style="display: inline-flex; height: 20px; border-radius: 3px; overflow: hidden; font-family: Arial, sans-serif; font-size: 11px; line-height: 20px;">
                <div style="background-color: #555; color: white; padding: 0 6px; text-align: center; min-width: 48px;">ArgoCD</div>
                <div style="background-color: ${color}; color: white; padding: 0 6px; text-align: center; min-width: ${status.length * 7 + 10}px;">${status}</div>
            </div>`
        }

        static getLatestVersion(instance, versions) {
            if (instance.latestVersion) {
                return instance.latestVersion
            }

            for (const v of versions) {
                if (v.Repository.URL != instance.Version.Repository.URL) {
                    continue
                }
                if (v.Prefix != instance.Version.Prefix) {
                    continue
                }

                instance.latestVersion = v
                return v
            }
        }
    }

    class SemVer {

        static semverRegex = /^v?(\d+)\.(\d+)\.(\d+)(?:-(.+))?$/

        constructor(version) {
            const match = version.match(SemVer.semverRegex)

            if (!match) {
                return
            }

            this.version = match[0]
            this.major = parseInt(match[1], 10)
            this.minor = parseInt(match[2], 10)
            this.patch = parseInt(match[3], 10)
        }
    }

    class Update {

        static TypeMajor = "Major"
        static TypeMinor = "Minor"
        static TypePatch = "Patch"
        static TypeNone = "None"

        constructor(one, two) {
            if (!one.major || !two.major) {
                this.type = Update.TypeNone
            }

            if (one.major !== two.major) {
                this.type = Update.TypeMajor
            } else if (one.minor !== two.minor) {
                this.type = Update.TypeMinor
            } else if (one.patch !== two.patch) {
                this.type = Update.TypePatch
            } else {
                this.type = Update.TypeNone
            }
        }

        getType() {
            return this.type
        }

        tailwindBorderClasses() {
            let classes = "border-l-8"
            switch (this.type) {
                case Update.TypePatch:
                    classes += " border-l-vf-aqua-blue"
                    break;
                case Update.TypeMinor:
                    classes += " border-l-vf-lemon-yellow"
                    break;
                case Update.TypeMajor:
                    classes += " border-l-vf-orange"
                    break;
                case Update.TypeNone:
                    classes += " border-l-vf-spring-green"
                    break;
            }
            return classes
        }
    }

    class UI {
        constructor() {
            this.contentElement = document.getElementById("content")
            this.searchForm = document.getElementById("searchForm")

            if (instances.length === 0) {
                this.setContent(`
                <div class="w-full flex flex-col items-center text-vf-gray italic">
                    <div>No instances found for the current search.</div>
                    <div class="mt-2">
                        Use the text box to search for any property like AWS region, account short name or application name.
                        Use commas to search for multiple terms simultaneously while an instance has to match all of them.
                        Use double quotes to request exact matches (e.g. <span class="italic">"datadog"</span> does not match <span class="italic">datadog-psql</span> )
                        Use the checkboxes to limit your search to one kind only, if needed.
                    </div>
                    <div class="mt-2">
                        Please note: To not overload the dashboard, <span class="font-semibold">you cannot get all instances at once</span>. You must use at least
                        one search option to get results.
                    </div>
                </div>
                `)
                return
            }

            document.querySelectorAll('#updateTypes input[type=checkbox]').forEach(box => {
                box.addEventListener('change', () => {
                    this.render()
                })
            })

            this.render()
        }

        setContent(content) {
            this.contentElement.innerHTML = content
        }

        async render() {
            let filterUpdateTypes = []
            const urlParams = new URLSearchParams(window.location.search)
            urlParams.delete("updateType")

            let formData = new FormData(this.searchForm)
            for (const touple of formData.entries()) {
                if (touple[0] === "updateType") {
                    filterUpdateTypes.push(touple[1])
                    urlParams.append('updateType', touple[1]);
                }
            }

            const newUrl = `${window.location.pathname}?${urlParams.toString()}`;
            history.pushState({}, '', newUrl);

            const instanceElements = instances
                .map(i => new Instance(i, versions))
                .filter(i => filterUpdateTypes.length === 0 || filterUpdateTypes.indexOf(i.getUpdateType()) !== -1)
                .map((i) => i.createHtmlElement(i, versions))
            if (!instanceElements || instanceElements.length === 0) {
                this.setContent('')
                return
            }
            this.setContent(`
                <h1 class="text-4xl font-bold pb-2">Instances</h1>
                <div class="flex flex-row flex-wrap justify-between pb-12">
                    ${instanceElements.reduce((a,b) => a+b)}
                </div>`)
        }
    }

    // Initialize the UI
    const ui = new UI()
})
